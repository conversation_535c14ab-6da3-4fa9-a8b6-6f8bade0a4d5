import type { LinkReference } from '$lib/activitypub/types/Core/Link';
import type { CoreObject } from '$lib/activitypub/types/Core';
import type { OrArray } from '$lib/activitypub/types/util';
import {
  getUrlFromAPObjectSafe,
  getUrlFromAPObjectRequired,
  getUrlsFromAPObjects,
  UrlValidationPresets,
  type UrlValidationOptions
} from '$lib/activitypub/utils/links';

/**
 * Extract URL from ActivityPub object (backward compatibility)
 * @deprecated Use getUrlFromAPObjectSafe for new code
 */
export function getUrlFromAPObject(object: OrArray<CoreObject | LinkReference | string | URL>): string | null {
  return getUrlFromAPObjectSafe(object, UrlValidationPresets.activityPub);
}

/**
 * Extract URL safely with validation
 */
export function getUrlSafe(
  object: OrArray<CoreObject | LinkReference | string | URL>,
  options?: UrlValidationOptions
): string | null {
  return getUrlFromAPObjectSafe(object, options);
}

/**
 * Extract URL with error throwing for required fields
 */
export function getUrlRequired(
  object: OrArray<CoreObject | LinkReference | string | URL>,
  fieldName: string,
  options?: UrlValidationOptions
): string {
  return getUrlFromAPObjectRequired(object, fieldName, options);
}

/**
 * Extract multiple URLs from objects
 */
export function getUrls(
  objects: OrArray<CoreObject | LinkReference | string | URL>,
  options?: UrlValidationOptions
): string[] {
  return getUrlsFromAPObjects(objects, options);
}

// Re-export utilities for convenience
export {
  validateUrl,
  normalizeUrl,
  isSameDomain,
  isExternalUrl,
  UrlValidationPresets,
  type UrlValidationOptions
} from '$lib/activitypub/utils/links';
