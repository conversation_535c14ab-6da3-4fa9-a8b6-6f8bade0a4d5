import {
  type Collection,
  type CollectionPage,
  type Entity,
  narrowByType,
  type Notelike,
  type OrderedCollection
} from '$lib/activitypub/types';
import { getVisibilityFromPost, importPost } from '$lib/activitypub/posts';
import { user as userTable } from '$lib/server/db/schema';
import {
  fetchAndProcessAPObject,
  isSupportedPostObject,
  createProcessingContext,
  checkProcessingSafety,
  processWithTimeout,
  mergeProcessingResults,
  SUPPORTED_POST_TYPES,
  type ProcessingContext,
  type ProcessingResult
} from '$lib/activitypub/utils';
import { getUrlSafe, UrlValidationPresets } from '$lib/activitypub/link';

// Type aliases for backward compatibility
type BackfillResult = ProcessingResult;
type BackfillContext = ProcessingContext;

// Security limits for backfill operations
const SECURITY_LIMITS = {
  MAX_COLLECTION_SIZE: 1000,
  MAX_RECURSION_DEPTH: 30,
  MAX_PROCESSING_TIME: 300000, // 5 minutes
  MAX_CONCURRENT_REQUESTS: 5,
  MAX_URL_LENGTH: 2048,
  MAX_OBJECT_SIZE: 1024 * 1024, // 1MB
  RATE_LIMIT_DELAY: 100 // ms between requests
} as const;

// Remove duplicate functions - now using utils

/**
 * Security monitoring and logging
 */
interface SecurityMetrics {
  totalRequests: number;
  rejectedUrls: number;
  oversizedObjects: number;
  suspiciousPatterns: number;
  rateLimitHits: number;
  processingTimeouts: number;
}

const securityMetrics: SecurityMetrics = {
  totalRequests: 0,
  rejectedUrls: 0,
  oversizedObjects: 0,
  suspiciousPatterns: 0,
  rateLimitHits: 0,
  processingTimeouts: 0
};

/**
 * Log security event and update metrics
 */
function logSecurityEvent(event: keyof SecurityMetrics, details?: string) {
  securityMetrics[event]++;
  console.warn(`Security event [${event}]: ${details || 'No details'}`);

  // Log summary every 100 events
  if (securityMetrics.totalRequests % 100 === 0) {
    console.log('Security metrics:', securityMetrics);
  }
}

// Security metrics functions removed - not used in current implementation

/**
 * Process item with timeout and error handling - using new utils
 */
async function processItemWithTimeout(
  item: any,
  context: BackfillContext,
  isRoot: boolean
): Promise<boolean> {
  const itemTimeout = Math.min(5000, context.timeout - (Date.now() - context.startTime));

  if (itemTimeout <= 0) {
    return false; // Skip due to global timeout
  }

  const result = await processWithTimeout(
    () => processItemSafely(item, context, isRoot),
    itemTimeout
  );

  if (!result.success) {
    console.warn(`Item processing failed:`, result.error);
    return false;
  }

  return result.result || false;
}

/**
 * Safely process individual item with enhanced security checks
 */
async function processItemSafely(item: any, context: BackfillContext, isRoot: boolean): Promise<boolean> {
  try {
    // Validate item structure
    if (!item) {
      return false;
    }

    let url: string | null = null;

    // Extract URL safely with validation
    if (item instanceof URL) {
      url = item.toString();
    } else if (typeof item === 'string') {
      url = item;
    } else if (typeof item === 'object') {
      // Try different URL extraction methods
      if (item.id) {
        url = getUrlSafe(item.id, UrlValidationPresets.activityPub);
      } else if (item.url) {
        url = getUrlSafe(item.url, UrlValidationPresets.activityPub);
      } else if (item.href) {
        url = getUrlSafe(item.href, UrlValidationPresets.activityPub);
      }
    }

    // Validate URL
    if (!url) {
      return false;
    }

    // Additional security checks
    if (url.length > SECURITY_LIMITS.MAX_URL_LENGTH) {
      logSecurityEvent('rejectedUrls', `URL too long: ${url.length} chars`);
      return false;
    }

    // Check if URL is already being processed
    if (context.visitedUrls.has(url)) {
      return false;
    }

    const result = await backfillPost(url, isRoot);
    return result;
  } catch (error) {
    console.warn('Failed to process item safely:', error);
    return false;
  }
}

export async function startBackfill(user: typeof userTable.$inferSelect) {
  console.log('Backfilling ' + user.username + '@' + user.domain);

  // Create context with enhanced security limits
  const context = createProcessingContext({
    startTime: Date.now(),
    timeout: SECURITY_LIMITS.MAX_PROCESSING_TIME,
    maxDepth: SECURITY_LIMITS.MAX_RECURSION_DEPTH,
    maxCollectionSize: SECURITY_LIMITS.MAX_COLLECTION_SIZE
  });

  let totalResult: BackfillResult = { processed: 0, errors: 0, skipped: 0, warnings: [] };

  try {
    // Process featured posts first
    if (user.featured) {
      console.log('Processing featured posts...');

      // Validate featured URL
      const featuredUrl = getUrlSafe(user.featured, UrlValidationPresets.activityPub);
      if (!featuredUrl) {
        totalResult.warnings.push('Invalid featured URL, skipping');
      } else {
        const featuredResult = await backfillCollectionSafe(featuredUrl, 20, true, context);
        totalResult = mergeProcessingResults(totalResult, featuredResult);
      }
    }

    // Process main outbox
    if (user.outbox) {
      console.log('Processing outbox...');

      // Validate outbox URL
      const outboxUrl = getUrlSafe(user.outbox, UrlValidationPresets.activityPub);
      if (!outboxUrl) {
        totalResult.warnings.push('Invalid outbox URL, skipping');
      } else {
        // Reset context for outbox processing but keep security limits
        context.visitedUrls.clear();
        context.currentDepth = 0;

        const outboxResult = await backfillCollectionSafe(outboxUrl, 20, true, context);
        totalResult = mergeProcessingResults(totalResult, outboxResult);
      }
    }

    console.log(`Backfill completed: ${totalResult.processed} processed, ${totalResult.errors} errors, ${totalResult.skipped} skipped`);
    if (totalResult.warnings.length > 0) {
      console.warn('Backfill warnings:', totalResult.warnings);
    }
  } catch (error) {
    console.error('Critical error during backfill:', error);
    totalResult.errors++;
    totalResult.warnings.push(`Critical backfill error: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  return totalResult;
}

/**
 * Process items safely with error handling and limits
 */
async function processItemsSafely(
  items: any[],
  context: BackfillContext,
  limit: number,
  isRoot: boolean
): Promise<BackfillResult> {
  const result: BackfillResult = {
    processed: 0,
    errors: 0,
    skipped: 0,
    warnings: []
  };

  // Enhanced collection size validation
  const maxSize = Math.min(context.maxCollectionSize, SECURITY_LIMITS.MAX_COLLECTION_SIZE);
  const safeItems = items.slice(0, maxSize);

  if (items.length > maxSize) {
    result.warnings.push(`Collection truncated for security: ${items.length} -> ${maxSize}`);
  }

  // Additional validation for suspicious patterns
  if (items.length > 10000) {
    logSecurityEvent('suspiciousPatterns', `Extremely large collection: ${items.length} items`);
    result.warnings.push(`Extremely large collection detected: ${items.length} items`);
  }

  for (const item of safeItems) {
    if (limit > 0 && result.processed >= limit) {
      break;
    }

    try {
      const processed = await processItemWithTimeout(item, context, isRoot);
      if (processed) {
        result.processed++;
      } else {
        result.skipped++;
      }
    } catch (error) {
      result.errors++;
      result.warnings.push(`Failed to process item: ${error instanceof Error ? error.message : 'Unknown error'}`);
      // Continue processing other items
    }
  }

  return result;
}

/**
 * Safe version of backfillCollection with resilient error handling
 */
async function backfillCollectionSafe(
  url: string,
  limit = 20,
  isRoot = false,
  context = createProcessingContext()
): Promise<BackfillResult> {
  const safety = checkProcessingSafety(url, context);
  if (!safety.safe) {
    return {
      processed: 0,
      errors: 0,
      skipped: 1,
      warnings: [safety.reason!]
    };
  }

  context.visitedUrls.add(url);
  context.currentDepth++;

  try {
    const fetchResult = await fetchAndProcessAPObject(url, { timeout: 10000 });

    if (!fetchResult.success) {
      return {
        processed: 0,
        errors: 1,
        skipped: 0,
        warnings: [`Failed to fetch collection: ${fetchResult.error}`]
      };
    }

    const result = fetchResult.data as Entity;

    // Handle single posts
    if (narrowByType<Notelike>(result, [...SUPPORTED_POST_TYPES])) {
      try {
        const processed = await backfillPost(result, isRoot);
        return {
          processed: processed ? 1 : 0,
          errors: 0,
          skipped: processed ? 0 : 1,
          warnings: []
        };
      } catch (error) {
        return {
          processed: 0,
          errors: 1,
          skipped: 0,
          warnings: [`Failed to process post: ${error instanceof Error ? error.message : 'Unknown error'}`]
        };
      }
    }

    // Validate collection type
    if (!narrowByType<Collection | OrderedCollection>(result, ['Collection', 'OrderedCollection'])) {
      return {
        processed: 0,
        errors: 0,
        skipped: 1,
        warnings: ['Not a valid collection type']
      };
    }

    // Process collection items
    if (result.orderedItems || result.items) {
      let items = result.orderedItems ?? result.items;
      if (items) {
        if (!Array.isArray(items)) {
          items = [items];
        }

        return await processItemsSafely(items, context, limit, isRoot);
      }
    }

    // Process pagination with URL validation
    if (result.first) {
      const firstUrl = getUrlSafe(result.first, UrlValidationPresets.activityPub);
      if (firstUrl) {
        return await backfillCollectionPageSafe(firstUrl, limit, 0, isRoot, context);
      } else {
        return {
          processed: 0,
          errors: 0,
          skipped: 1,
          warnings: ['Invalid first page URL in collection']
        };
      }
    }

    return { processed: 0, errors: 0, skipped: 0, warnings: ['Empty collection'] };

  } catch (error) {
    return {
      processed: 0,
      errors: 1,
      skipped: 0,
      warnings: [`Collection processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`]
    };
  } finally {
    context.currentDepth--;
  }
}

// Legacy function for backward compatibility - now uses safe implementation
export async function backfillCollection(collection: string, limit: number = 0, isRoot: boolean = false) {
  const context = createProcessingContext({ startTime: Date.now() });
  const result = await backfillCollectionSafe(collection, limit, isRoot, context);

  if (result.errors > 0) {
    console.warn(`Backfill completed with ${result.errors} errors and ${result.warnings.length} warnings`);
    result.warnings.forEach(warning => console.warn(warning));
  }

  return result.processed;
}

/**
 * Safe version of backfillCollectionPage with resilient error handling
 */
async function backfillCollectionPageSafe(
  url: string,
  limit = 20,
  processed = 0,
  isRoot = false,
  context = createProcessingContext()
): Promise<BackfillResult> {
  const safety = checkProcessingSafety(url, context);
  if (!safety.safe) {
    return {
      processed: 0,
      errors: 0,
      skipped: 1,
      warnings: [safety.reason!]
    };
  }

  context.visitedUrls.add(url);
  context.currentDepth++;

  try {
    const fetchResult = await fetchAndProcessAPObject<CollectionPage>(url, { timeout: 10000 });

    if (!fetchResult.success) {
      return {
        processed: 0,
        errors: 1,
        skipped: 0,
        warnings: [`Failed to fetch collection page: ${fetchResult.error}`]
      };
    }

    const result = fetchResult.data!;

    const pageResult: BackfillResult = {
      processed: 0,
      errors: 0,
      skipped: 0,
      warnings: []
    };

    // Process items on this page
    if (result.orderedItems || result.items) {
      let items = result.orderedItems ?? result.items;
      if (items) {
        if (!Array.isArray(items)) {
          items = [items];
        }

        const itemsResult = await processItemsSafely(items, context, limit - processed, isRoot);
        pageResult.processed += itemsResult.processed;
        pageResult.errors += itemsResult.errors;
        pageResult.skipped += itemsResult.skipped;
        pageResult.warnings.push(...itemsResult.warnings);
      }
    }

    // Process next page if we haven't reached the limit
    if (result.next && (limit === 0 || pageResult.processed + processed < limit)) {
      const nextUrl = getUrlSafe(result.next, UrlValidationPresets.activityPub);
      if (nextUrl) {
        // Add rate limiting delay between page requests
        await new Promise(resolve => setTimeout(resolve, SECURITY_LIMITS.RATE_LIMIT_DELAY));

        const nextResult = await backfillCollectionPageSafe(
          nextUrl,
          limit,
          processed + pageResult.processed,
          isRoot,
          context
        );
        pageResult.processed += nextResult.processed;
        pageResult.errors += nextResult.errors;
        pageResult.skipped += nextResult.skipped;
        pageResult.warnings.push(...nextResult.warnings);
      } else {
        pageResult.warnings.push('Invalid next page URL, stopping pagination');
      }
    }

    return pageResult;

  } catch (error) {
    return {
      processed: 0,
      errors: 1,
      skipped: 0,
      warnings: [`Collection page processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`]
    };
  } finally {
    context.currentDepth--;
  }
}

// Legacy backfillCollectionPage function removed - use backfillCollectionPageSafe instead

// processUrl function removed - functionality integrated into other functions

// processObject and processCreateActivity functions removed - functionality integrated into other functions

async function backfillPost(postObject: string | Notelike, isRoot: boolean = false): Promise<boolean> {
  try {
    if (typeof postObject === 'string') {
      // Validate URL before fetching
      const validUrl = getUrlSafe(postObject, UrlValidationPresets.activityPub);
      if (!validUrl) {
        console.warn(`Invalid post URL rejected: ${postObject}`);
        return false;
      }

      // Fetch and process the object using centralized utility
      const fetchResult = await fetchAndProcessAPObject<Notelike>(validUrl, { timeout: 10000 });

      if (!fetchResult.success) {
        console.warn(`Failed to fetch object: ${fetchResult.error}`);
        return false;
      }

      const rawObject = fetchResult.data!;

      // Validate object structure
      if (!rawObject || typeof rawObject !== 'object') {
        console.warn('Invalid post object structure received');
        return false;
      }

      postObject = rawObject;
    }

    // Additional validation for the post object
    if (!postObject || typeof postObject !== 'object') {
      console.warn('Invalid post object provided');
      return false;
    }

    // Check if it's a supported post type
    if (!isSupportedPostObject(postObject)) {
      return false;
    }

    // Check visibility using existing function
    const visibility = getVisibilityFromPost(postObject);
    if (visibility !== 'public' && visibility !== 'unlisted') {
      return false;
    }

    // Skip replies if this is a root-level import
    if (isRoot && postObject.inReplyTo) {
      return false;
    }

    // Process the post with additional error handling
    await importPost(postObject);
    return true;
  } catch (error) {
    console.warn('Error processing post:', error);
    return false;
  }
}
