/**
 * HTTP Signature implementation for ActivityPub federation
 * Based on draft-cavage-http-signatures-12
 */

import { createHash, createSign, createVerify } from 'crypto';
import type { Actor } from '$lib/activitypub/types';
import type { HttpSignatureHeader, HttpSignatureVerificationResult } from '$lib/activitypub/types/federation';
import { federationConfig } from '$lib/activitypub/config/federation';
import { fetchAndProcessAPObject } from './fetch';
import { validateUrl } from './links';
import { 
  SignatureVerificationError, 
  SecurityError, 
  ValidationError,
  MissingFieldError 
} from './errors';
import { ActivityPubLogs } from './logger';

/**
 * Generate SHA-256 digest for request body
 */
export function generateDigest(body: string): string {
  const hash = createHash('sha256');
  hash.update(body, 'utf8');
  return `SHA-256=${hash.digest('base64')}`;
}

/**
 * Build signature string from headers
 */
export function buildSignatureString(
  method: string,
  path: string,
  headers: Record<string, string>,
  signatureHeaders: string[]
): string {
  const parts: string[] = [];
  
  for (const headerName of signatureHeaders) {
    if (headerName === '(request-target)') {
      parts.push(`(request-target): ${method.toLowerCase()} ${path}`);
    } else {
      const value = headers[headerName.toLowerCase()];
      if (value === undefined) {
        throw new MissingFieldError(`Required header missing: ${headerName}`);
      }
      parts.push(`${headerName.toLowerCase()}: ${value}`);
    }
  }
  
  return parts.join('\n');
}

/**
 * Parse signature header into components
 */
export function parseSignatureHeader(signatureHeader: string): HttpSignatureHeader {
  const params: Record<string, string> = {};
  
  // Parse signature parameters
  const paramRegex = /(\w+)="([^"]+)"/g;
  let match;
  
  while ((match = paramRegex.exec(signatureHeader)) !== null) {
    params[match[1]] = match[2];
  }
  
  if (!params.keyId || !params.algorithm || !params.headers || !params.signature) {
    throw new ValidationError('Invalid signature header format');
  }
  
  return {
    keyId: params.keyId,
    algorithm: params.algorithm,
    headers: params.headers.split(' '),
    signature: params.signature
  };
}

/**
 * Extract signature from request headers
 */
export function extractSignature(headers: Record<string, string>): HttpSignatureHeader | null {
  // Check Signature header first
  const signatureHeader = headers.signature || headers.Signature;
  if (signatureHeader) {
    return parseSignatureHeader(signatureHeader);
  }
  
  // Check Authorization header
  const authHeader = headers.authorization || headers.Authorization;
  if (authHeader && authHeader.startsWith('Signature ')) {
    return parseSignatureHeader(authHeader.substring(10));
  }
  
  return null;
}

/**
 * Sign an HTTP request
 */
export async function signRequest(
  method: string,
  url: string,
  headers: Record<string, string>,
  body: string | null,
  privateKey: string,
  keyId: string
): Promise<Record<string, string>> {
  const urlObj = new URL(url);
  const path = urlObj.pathname + urlObj.search;
  
  // Prepare headers
  const requestHeaders = { ...headers };
  
  // Add required headers
  if (!requestHeaders.host) {
    requestHeaders.host = urlObj.host;
  }
  
  if (!requestHeaders.date) {
    requestHeaders.date = new Date().toUTCString();
  }
  
  // Add digest for POST requests with body
  if (body && method.toUpperCase() === 'POST' && !requestHeaders.digest) {
    requestHeaders.digest = generateDigest(body);
  }
  
  // Build signature string
  const signatureHeaders = federationConfig.httpSignature.headers;
  const signatureString = buildSignatureString(method, path, requestHeaders, signatureHeaders);
  
  // Sign the string
  const sign = createSign(federationConfig.httpSignature.algorithm);
  sign.update(signatureString);
  const signature = sign.sign(privateKey, 'base64');
  
  // Build signature header
  const signatureHeader = [
    `keyId="${keyId}"`,
    `algorithm="${federationConfig.httpSignature.algorithm}"`,
    `headers="${signatureHeaders.join(' ')}"`,
    `signature="${signature}"`
  ].join(',');
  
  requestHeaders.signature = signatureHeader;
  
  ActivityPubLogs.security.signatureGenerated(keyId, url);
  
  return requestHeaders;
}

/**
 * Fetch actor's public key
 */
async function fetchActorPublicKey(keyId: string): Promise<string> {
  // Extract actor URL from keyId (usually keyId is actorUrl#main-key)
  const actorUrl = keyId.split('#')[0];
  
  if (!validateUrl(actorUrl)) {
    throw new ValidationError(`Invalid actor URL in keyId: ${keyId}`);
  }
  
  try {
    const result = await fetchAndProcessAPObject(actorUrl);
    if (!result.success || !result.data) {
      throw new SecurityError(`Failed to fetch actor: ${actorUrl}`, 'ACTOR_FETCH_FAILED');
    }
    
    const actor = result.data as Actor;
    
    // Look for public key in publicKey field
    if (actor.publicKey) {
      if (typeof actor.publicKey === 'string') {
        return actor.publicKey;
      } else if (actor.publicKey.publicKeyPem) {
        return actor.publicKey.publicKeyPem;
      }
    }
    
    throw new SecurityError(`No public key found for actor: ${actorUrl}`, 'PUBLIC_KEY_NOT_FOUND');
    
  } catch (error) {
    if (error instanceof SecurityError) {
      throw error;
    }
    throw new SecurityError(
      `Failed to fetch public key for ${keyId}: ${error instanceof Error ? error.message : 'Unknown error'}`,
      'PUBLIC_KEY_FETCH_FAILED'
    );
  }
}

/**
 * Verify HTTP signature
 */
export async function verifySignature(
  method: string,
  url: string,
  headers: Record<string, string>,
  body: string | null,
  signature: HttpSignatureHeader
): Promise<HttpSignatureVerificationResult> {
  try {
    // Check algorithm
    if (signature.algorithm !== federationConfig.httpSignature.algorithm) {
      return {
        valid: false,
        error: `Unsupported algorithm: ${signature.algorithm}`
      };
    }
    
    // Check clock skew if date header is present
    const dateHeader = headers.date;
    if (dateHeader) {
      const requestTime = new Date(dateHeader).getTime();
      const now = Date.now();
      const skew = Math.abs(now - requestTime) / 1000;
      
      if (skew > federationConfig.httpSignature.clockSkewTolerance) {
        ActivityPubLogs.security.signatureVerificationFailed(
          signature.keyId,
          url,
          `Clock skew too large: ${skew}s`
        );
        return {
          valid: false,
          error: `Clock skew too large: ${skew} seconds`
        };
      }
    }
    
    // Verify digest if present
    if (body && headers.digest) {
      const expectedDigest = generateDigest(body);
      if (headers.digest !== expectedDigest) {
        ActivityPubLogs.security.signatureVerificationFailed(
          signature.keyId,
          url,
          'Digest mismatch'
        );
        return {
          valid: false,
          error: 'Digest verification failed'
        };
      }
    }
    
    // Fetch public key
    const publicKey = await fetchActorPublicKey(signature.keyId);
    
    // Build signature string
    const urlObj = new URL(url);
    const path = urlObj.pathname + urlObj.search;
    const signatureString = buildSignatureString(method, path, headers, signature.headers);
    
    // Verify signature
    const verify = createVerify(signature.algorithm);
    verify.update(signatureString);
    const isValid = verify.verify(publicKey, signature.signature, 'base64');
    
    if (isValid) {
      ActivityPubLogs.security.signatureVerified(signature.keyId, url);
      return {
        valid: true,
        keyId: signature.keyId
      };
    } else {
      ActivityPubLogs.security.signatureVerificationFailed(
        signature.keyId,
        url,
        'Signature verification failed'
      );
      return {
        valid: false,
        error: 'Signature verification failed'
      };
    }
    
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    ActivityPubLogs.security.signatureVerificationFailed(
      signature.keyId,
      url,
      errorMessage
    );
    
    return {
      valid: false,
      error: errorMessage
    };
  }
}

/**
 * Validate incoming request signature
 */
export async function validateIncomingSignature(
  method: string,
  url: string,
  headers: Record<string, string>,
  body: string | null
): Promise<HttpSignatureVerificationResult> {
  // Extract signature from headers
  const signature = extractSignature(headers);
  
  if (!signature) {
    if (federationConfig.security.requireHttpSignature) {
      return {
        valid: false,
        error: 'HTTP signature required but not found'
      };
    } else {
      // Signature not required, consider valid
      return { valid: true };
    }
  }
  
  return verifySignature(method, url, headers, body, signature);
}

/**
 * Middleware for HTTP signature validation
 */
export function createSignatureValidationMiddleware() {
  return async (
    method: string,
    url: string,
    headers: Record<string, string>,
    body: string | null
  ): Promise<{ valid: boolean; error?: string; keyId?: string }> => {
    try {
      const result = await validateIncomingSignature(method, url, headers, body);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Signature validation failed';
      ActivityPubLogs.security.signatureVerificationFailed('unknown', url, errorMessage);
      
      return {
        valid: false,
        error: errorMessage
      };
    }
  };
}