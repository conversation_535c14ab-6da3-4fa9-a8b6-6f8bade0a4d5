/**
 * Content processing utilities for ActivityPub objects
 * Handles Markdown, mentions, hashtags, media, and content sanitization
 */

import type { Mention, ExtendedObject } from '$lib/activitypub/types';
import { escapeHtml, isSafeContent } from './security';
import { logger } from './logger';

/**
 * Mention extraction result
 */
export interface MentionMatch {
  text: string;           // Original text like "@<EMAIL>"
  username: string;       // "user"
  domain?: string;        // "domain.com" (undefined for local mentions)
  startIndex: number;     // Position in text
  endIndex: number;       // End position in text
}

/**
 * Hashtag extraction result
 */
export interface HashtagMatch {
  text: string;           // Original text like "#hashtag"
  tag: string;            // "hashtag" (without #)
  startIndex: number;     // Position in text
  endIndex: number;       // End position in text
}

/**
 * Media file information
 */
export interface MediaInfo {
  type: 'image' | 'video' | 'audio' | 'document' | 'unknown';
  mimeType: string;
  size?: number;
  width?: number;
  height?: number;
  duration?: number;
  isValid: boolean;
  errors: string[];
}

/**
 * Content processing options
 */
export interface ContentProcessingOptions {
  allowMarkdown?: boolean;
  extractMentions?: boolean;
  extractHashtags?: boolean;
  sanitizeHtml?: boolean;
  maxLength?: number;
  allowedTags?: string[];
  baseUrl?: string;
}

/**
 * Content processing result
 */
export interface ProcessedContent {
  html: string;
  text: string;
  mentions: MentionMatch[];
  hashtags: HashtagMatch[];
  links: Mention[];
  tags: ExtendedObject[];
  warnings: string[];
  errors: string[];
}

/**
 * Default content processing options
 */
export const DEFAULT_CONTENT_OPTIONS: ContentProcessingOptions = {
  allowMarkdown: true,
  extractMentions: true,
  extractHashtags: true,
  sanitizeHtml: true,
  maxLength: 50000,
  allowedTags: ['p', 'br', 'strong', 'em', 'a', 'ul', 'ol', 'li', 'blockquote', 'code', 'pre'],
  baseUrl: undefined
};

/**
 * Extract mentions from text
 */
export function extractMentions(text: string): MentionMatch[] {
  const mentions: MentionMatch[] = [];
  
  // Pattern for @<EMAIL> or @username
  const mentionRegex = /@([a-zA-Z0-9_.-]+)(?:@([a-zA-Z0-9.-]+\.[a-zA-Z]{2,}))?/g;
  
  let match;
  while ((match = mentionRegex.exec(text)) !== null) {
    mentions.push({
      text: match[0],
      username: match[1],
      domain: match[2],
      startIndex: match.index,
      endIndex: match.index + match[0].length
    });
  }
  
  return mentions;
}

/**
 * Extract hashtags from text
 */
export function extractHashtags(text: string): HashtagMatch[] {
  const hashtags: HashtagMatch[] = [];
  
  // Pattern for #hashtag (letters, numbers, underscore, but not starting with number)
  const hashtagRegex = /#([a-zA-Z_][a-zA-Z0-9_]*)/g;
  
  let match;
  while ((match = hashtagRegex.exec(text)) !== null) {
    hashtags.push({
      text: match[0],
      tag: match[1],
      startIndex: match.index,
      endIndex: match.index + match[0].length
    });
  }
  
  return hashtags;
}

/**
 * Convert mentions to ActivityPub Mention objects
 */
export function mentionsToLinks(mentions: MentionMatch[], baseUrl?: string): Mention[] {
  return mentions.map(mention => {
    const href = mention.domain
      ? `https://${mention.domain}/users/${mention.username}`
      : baseUrl
        ? `${baseUrl}/users/${mention.username}`
        : `/@${mention.username}`;

    return {
      type: 'Mention',
      href: new URL(href),
      name: mention.text
    };
  });
}

/**
 * Convert hashtags to ActivityPub Hashtag objects
 */
export function hashtagsToTags(hashtags: HashtagMatch[], baseUrl?: string): ExtendedObject[] {
  return hashtags.map(hashtag => {
    const href = baseUrl
      ? `${baseUrl}/tags/${hashtag.tag.toLowerCase()}`
      : `#${hashtag.tag}`;

    return {
      type: 'Hashtag',
      href: new URL(href),
      name: hashtag.text
    };
  });
}

/**
 * Basic Markdown to HTML conversion
 */
export function markdownToHtml(markdown: string): string {
  let html = markdown;
  
  // Headers
  html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>');
  html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>');
  html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>');
  
  // Bold
  html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
  html = html.replace(/__(.*?)__/g, '<strong>$1</strong>');
  
  // Italic
  html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');
  html = html.replace(/_(.*?)_/g, '<em>$1</em>');
  
  // Code blocks
  html = html.replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>');
  
  // Inline code
  html = html.replace(/`(.*?)`/g, '<code>$1</code>');
  
  // Links
  html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>');
  
  // Line breaks
  html = html.replace(/\n\n/g, '</p><p>');
  html = html.replace(/\n/g, '<br>');
  
  // Wrap in paragraphs
  if (html && !html.startsWith('<')) {
    html = '<p>' + html + '</p>';
  }
  
  return html;
}

/**
 * Sanitize HTML content while preserving allowed tags
 */
export function sanitizeHtml(html: string, allowedTags: string[] = DEFAULT_CONTENT_OPTIONS.allowedTags!): string {
  // Basic implementation - in production, use a proper HTML sanitizer library
  let sanitized = html;
  
  // Remove script tags and their content
  sanitized = sanitized.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
  
  // Remove dangerous attributes
  sanitized = sanitized.replace(/\s*on\w+\s*=\s*["'][^"']*["']/gi, '');
  sanitized = sanitized.replace(/\s*javascript\s*:/gi, '');
  
  // Remove style attributes (basic XSS prevention)
  sanitized = sanitized.replace(/\s*style\s*=\s*["'][^"']*["']/gi, '');
  
  // Allow only specified tags (very basic implementation)
  const tagRegex = /<\/?([a-zA-Z][a-zA-Z0-9]*)\b[^>]*>/g;
  sanitized = sanitized.replace(tagRegex, (match, tagName) => {
    if (allowedTags.includes(tagName.toLowerCase())) {
      return match;
    }
    return '';
  });
  
  return sanitized;
}

/**
 * Get media type from MIME type
 */
export function getMediaType(mimeType: string): MediaInfo['type'] {
  if (mimeType.startsWith('image/')) return 'image';
  if (mimeType.startsWith('video/')) return 'video';
  if (mimeType.startsWith('audio/')) return 'audio';
  if (mimeType.startsWith('text/') || mimeType.includes('pdf') || mimeType.includes('document')) {
    return 'document';
  }
  return 'unknown';
}

/**
 * Validate media file
 */
export function validateMediaFile(
  mimeType: string, 
  size?: number, 
  width?: number, 
  height?: number,
  duration?: number
): MediaInfo {
  const errors: string[] = [];
  const type = getMediaType(mimeType);
  
  // Size limits (in bytes)
  const sizeLimits = {
    image: 10 * 1024 * 1024,    // 10MB
    video: 100 * 1024 * 1024,   // 100MB
    audio: 50 * 1024 * 1024,    // 50MB
    document: 20 * 1024 * 1024, // 20MB
    unknown: 5 * 1024 * 1024    // 5MB
  };
  
  // Check file size
  if (size !== undefined) {
    const limit = sizeLimits[type];
    if (size > limit) {
      errors.push(`File size ${size} exceeds limit ${limit} for ${type} files`);
    }
  }
  
  // Check image dimensions
  if (type === 'image' && width !== undefined && height !== undefined) {
    const maxDimension = 4096;
    if (width > maxDimension || height > maxDimension) {
      errors.push(`Image dimensions ${width}x${height} exceed maximum ${maxDimension}x${maxDimension}`);
    }
  }
  
  // Check video/audio duration
  if ((type === 'video' || type === 'audio') && duration !== undefined) {
    const maxDuration = type === 'video' ? 3600 : 7200; // 1 hour for video, 2 hours for audio
    if (duration > maxDuration) {
      errors.push(`${type} duration ${duration}s exceeds maximum ${maxDuration}s`);
    }
  }
  
  // Check MIME type whitelist
  const allowedMimeTypes = [
    // Images
    'image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml',
    // Videos
    'video/mp4', 'video/webm', 'video/ogg',
    // Audio
    'audio/mp3', 'audio/ogg', 'audio/wav', 'audio/m4a',
    // Documents
    'text/plain', 'application/pdf'
  ];
  
  if (!allowedMimeTypes.includes(mimeType)) {
    errors.push(`MIME type ${mimeType} is not allowed`);
  }
  
  return {
    type,
    mimeType,
    size,
    width,
    height,
    duration,
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Process content with all available features
 */
export function processContent(
  content: string, 
  options: ContentProcessingOptions = DEFAULT_CONTENT_OPTIONS
): ProcessedContent {
  const opts = { ...DEFAULT_CONTENT_OPTIONS, ...options };
  const warnings: string[] = [];
  const errors: string[] = [];
  
  let processedText = content;
  let processedHtml = content;
  
  // Check content length
  if (opts.maxLength && content.length > opts.maxLength) {
    errors.push(`Content length ${content.length} exceeds maximum ${opts.maxLength}`);
    processedText = content.substring(0, opts.maxLength);
    processedHtml = processedText;
  }
  
  // Check content safety
  if (!isSafeContent(content)) {
    warnings.push('Content contains potentially dangerous patterns');
  }
  
  // Extract mentions and hashtags
  const mentions = opts.extractMentions ? extractMentions(processedText) : [];
  const hashtags = opts.extractHashtags ? extractHashtags(processedText) : [];
  
  // Convert to ActivityPub objects
  const links = mentionsToLinks(mentions, opts.baseUrl);
  const tags = hashtagsToTags(hashtags, opts.baseUrl);
  
  // Process Markdown
  if (opts.allowMarkdown) {
    processedHtml = markdownToHtml(processedText);
  }
  
  // Sanitize HTML
  if (opts.sanitizeHtml) {
    processedHtml = sanitizeHtml(processedHtml, opts.allowedTags);
  }
  
  // Log processing
  logger.debug('Content processed', {
    operation: 'content.processContent',
    metadata: {
      originalLength: content.length,
      processedLength: processedText.length,
      mentionsCount: mentions.length,
      hashtagsCount: hashtags.length,
      hasWarnings: warnings.length > 0,
      hasErrors: errors.length > 0
    }
  });
  
  return {
    html: processedHtml,
    text: processedText,
    mentions,
    hashtags,
    links,
    tags,
    warnings,
    errors
  };
}

/**
 * Extract URLs from text
 */
export function extractUrls(text: string): string[] {
  const urlRegex = /https?:\/\/[^\s<>"{}|\\^`[\]]+/g;
  return text.match(urlRegex) || [];
}

/**
 * Replace mentions and hashtags with HTML links
 */
export function linkifyContent(
  content: string,
  mentions: MentionMatch[],
  hashtags: HashtagMatch[],
  baseUrl?: string
): string {
  let result = content;

  // Sort by position (descending) to avoid index shifting
  const allMatches = [
    ...mentions.map(m => ({ ...m, type: 'mention' as const })),
    ...hashtags.map(h => ({ ...h, type: 'hashtag' as const }))
  ].sort((a, b) => b.startIndex - a.startIndex);

  for (const match of allMatches) {
    const before = result.substring(0, match.startIndex);
    const after = result.substring(match.endIndex);

    let replacement: string;
    if (match.type === 'mention') {
      const mention = match as MentionMatch & { type: 'mention' };
      const href = mention.domain
        ? `https://${mention.domain}/users/${mention.username}`
        : baseUrl
          ? `${baseUrl}/users/${mention.username}`
          : `/@${mention.username}`;
      replacement = `<a href="${escapeHtml(href)}" class="mention">@${escapeHtml(mention.username)}${mention.domain ? '@' + escapeHtml(mention.domain) : ''}</a>`;
    } else {
      const hashtag = match as HashtagMatch & { type: 'hashtag' };
      const href = baseUrl
        ? `${baseUrl}/tags/${hashtag.tag.toLowerCase()}`
        : `#${hashtag.tag}`;
      replacement = `<a href="${escapeHtml(href)}" class="hashtag">#${escapeHtml(hashtag.tag)}</a>`;
    }

    result = before + replacement + after;
  }

  return result;
}

/**
 * Create media attachment object
 */
export function createMediaAttachment(
  url: string,
  mediaInfo: MediaInfo,
  name?: string,
  blurhash?: string
) {
  return {
    type: 'Document',
    mediaType: mediaInfo.mimeType,
    url,
    name: name || `${mediaInfo.type} attachment`,
    width: mediaInfo.width,
    height: mediaInfo.height,
    duration: mediaInfo.duration,
    blurhash
  };
}

/**
 * Validate and process multiple media files
 */
export function processMediaFiles(files: Array<{
  url: string;
  mimeType: string;
  size?: number;
  width?: number;
  height?: number;
  duration?: number;
  name?: string;
}>): {
  valid: Array<ReturnType<typeof createMediaAttachment>>;
  invalid: Array<{ file: typeof files[0]; errors: string[] }>;
  warnings: string[];
} {
  const valid = [];
  const invalid = [];
  const warnings = [];

  const maxFiles = 4; // ActivityPub recommendation

  if (files.length > maxFiles) {
    warnings.push(`Too many media files: ${files.length} > ${maxFiles}`);
  }

  for (const file of files.slice(0, maxFiles)) {
    const mediaInfo = validateMediaFile(
      file.mimeType,
      file.size,
      file.width,
      file.height,
      file.duration
    );

    if (mediaInfo.isValid) {
      valid.push(createMediaAttachment(file.url, mediaInfo, file.name));
    } else {
      invalid.push({ file, errors: mediaInfo.errors });
    }
  }

  return { valid, invalid, warnings };
}

/**
 * Clean and normalize text content
 */
export function normalizeTextContent(text: string): string {
  return text
    // Normalize whitespace
    .replace(/\s+/g, ' ')
    // Remove leading/trailing whitespace
    .trim()
    // Remove null bytes and control characters
    .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')
    // Normalize Unicode
    .normalize('NFC');
}

/**
 * Truncate content while preserving word boundaries
 */
export function truncateContent(content: string, maxLength: number, suffix = '...'): string {
  if (content.length <= maxLength) {
    return content;
  }

  const truncated = content.substring(0, maxLength - suffix.length);
  const lastSpace = truncated.lastIndexOf(' ');

  if (lastSpace > 0 && lastSpace > maxLength * 0.8) {
    return truncated.substring(0, lastSpace) + suffix;
  }

  return truncated + suffix;
}

/**
 * Count content statistics
 */
export function getContentStats(content: string): {
  characters: number;
  words: number;
  lines: number;
  mentions: number;
  hashtags: number;
  urls: number;
} {
  const mentions = extractMentions(content);
  const hashtags = extractHashtags(content);
  const urls = extractUrls(content);

  return {
    characters: content.length,
    words: content.trim().split(/\s+/).filter(word => word.length > 0).length,
    lines: content.split('\n').length,
    mentions: mentions.length,
    hashtags: hashtags.length,
    urls: urls.length
  };
}

/**
 * Check if content needs content warning
 */
export function needsContentWarning(content: string): { needed: boolean; reasons: string[] } {
  const reasons: string[] = [];
  const lowerContent = content.toLowerCase();

  // Check for sensitive topics (basic implementation)
  const sensitivePatterns = [
    /\b(death|suicide|self.?harm)\b/i,
    /\b(violence|abuse|assault)\b/i,
    /\b(nsfw|explicit|adult)\b/i,
    /\b(drug|alcohol|substance)\b/i
  ];

  for (const pattern of sensitivePatterns) {
    if (pattern.test(lowerContent)) {
      reasons.push(`Contains potentially sensitive content: ${pattern.source}`);
    }
  }

  // Check for excessive caps
  const capsRatio = (content.match(/[A-Z]/g) || []).length / content.length;
  if (capsRatio > 0.5 && content.length > 20) {
    reasons.push('Excessive use of capital letters');
  }

  return {
    needed: reasons.length > 0,
    reasons
  };
}
