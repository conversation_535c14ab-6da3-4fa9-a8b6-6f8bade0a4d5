/**
 * ActivityPub utilities index.
 *
 * This module provides a comprehensive set of utilities for working with ActivityPub objects,
 * including validation, normalization, processing, and error handling.
 */

// Object processing utilities
export {
  processAPObject,
  processUrlsInAPObject,
  processDatesInAPObject,
  getCollectionTotalItems,
  getCollectionItemsPageByPage,
  ObjectProcessingError,
  CircularReferenceError,
  type ProcessingOptions
} from './object';

// Re-export legacy functions for backward compatibility
export { processAPObject as processObject } from './object';

// Security utilities
export {
  escapeHtml,
  isSecureUrl,
  isValidDomain,
  isSafeContent
} from './security';

// Export constants
export {
  SUPPORTED_POST_TYPES,
  COLLECTION_TYPES,
  COLLECTION_PAGE_TYPES,
  ACTIVITY_TYPES,
  DEFAULT_PROCESSING_LIMITS,
  ACTIVITYPUB_HEADERS,
  type SupportedPostType
} from './constants';

// Export fetch utilities
export {
  fetchAndProcessAPObject,
  fetchMultipleAPObjects,
  fetchAPObjectWithRetry,
  fetchMultipleAPObjectsWithRetry,
  RetryFetchers,
  type FetchResult
} from './fetch';

// Export URL validation from links (avoiding duplication)
export { validateUrl as isValidActivityPubUrl } from './links';

// Export type utilities
export {
  getObjectType,
  isSupportedPostType,
  isSupportedPostObject,
  isCollectionType,
  isCollection,
  isCollectionPageType,
  isCollectionPage,
  isActivityType,
  isActivity,
  validateObjectStructure,
  getAllTypes,
  hasType
} from './types';

// Export processing utilities
export {
  createProcessingContext,
  checkProcessingSafety,
  processWithTimeout,
  processItemsBatch,
  mergeProcessingResults,
  type ProcessingContext,
  type ProcessingResult,
  type SafetyCheckResult
} from './processing';

// Export address utilities
export {
  normalizeAddresses,
  isFollowersCollection,
  isPublicCollection,
  extractAllAddresses,
  addressesContain,
  filterAddresses,
  AddressPatterns
} from './addresses';

// Export visibility utilities
export {
  getVisibilityFromPost,
  isPublicPost,
  isUnlistedPost,
  isFollowersOnlyPost,
  isDirectPost,
  isPubliclyDiscoverable,
  getPostRecipients,
  VisibilityLevels,
  compareVisibility,
  isVisibilityAtLeast,
  type PostVisibility
} from './visibility';

// Export collection utilities
export {
  getCollectionCounts,
  getSafeCollectionCount,
  batchGetCollectionCounts,
  processCollectionsWithStats,
  type CollectionCounts,
  type CollectionCountsResult,
  type CollectionStats
} from './collections';

// Export quote utilities
export {
  resolveQuoteUrl,
  resolveQuoteFromTags,
  isQuotePost,
  extractQuoteReferences,
  isValidQuoteUrl,
  normalizeQuoteUrl,
  isSelfQuote,
  extractQuoteContext,
  QUOTE_FIELDS,
  type QuoteField
} from './quotes';

// Export error utilities
export {
  ActivityPubError,
  NetworkError,
  FetchError,
  TimeoutError,
  RateLimitError,
  ValidationError,
  MissingFieldError,
  InvalidTypeError,
  MalformedObjectError,
  InvalidUrlError,
  ProcessingError,
  ActorImportError,
  PostImportError,
  WebFingerError,
  SecurityError,
  SignatureVerificationError,
  UnsafeContentError,
  isActivityPubError,
  isNetworkError,
  isValidationError,
  isProcessingError,
  isSecurityError,
  toActivityPubError,
  withErrorRecovery,
  type ErrorRecoveryOptions
} from './errors';

// Export activity utilities
export {
  isCreateActivity,
  isUpdateActivity,
  isDeleteActivity,
  isFollowActivity,
  isAcceptActivity,
  isRejectActivity,
  isAddActivity,
  isRemoveActivity,
  isLikeActivity,
  isAnnounceActivity,
  isUndoActivity,
  isBlockActivity,
  isFlagActivity,
  isValidActivity,
  validateActivity,
  extractActivityObjects,
  extractActivityActor,
  isTransitiveActivity,
  isIntransitiveActivity,
  getActivitySummary,
  validateActivityOrThrow,
  processActivitySafely,
  extractObjectUrls,
  processActivityObjects,
  ActivityProcessors
} from './activities';

// Export retry utilities
export {
  withRetry,
  withEnhancedRetry,
  retryFetch,
  retryActivityPubFetch,
  retryBatch,
  isRetryableError,
  calculateDelay,
  CircuitBreaker,
  RetryMetrics,
  globalRetryMetrics,
  RetryStrategies,
  ActivityPubRetryConfigs,
  type RetryStrategy,
  type RetryContext,
  type RetryResult
} from './retry';

// Export batch utilities
export {
  processBatch,
  batchFetchAPObjects,
  batchProcessCollections,
  batchImportActors,
  batchImportPosts,
  batchWithErrorHandling,
  BatchMetricsCollector,
  globalBatchMetrics,
  BatchConfigs,
  type BatchConfig,
  type BatchResult,
  type BatchProgressCallback,
  type BatchMetrics
} from './batch';

// Export logging utilities
export {
  ActivityPubLogger,
  ConsoleLogOutput,
  logger,
  ActivityPubLogs,
  createTimer,
  generateCorrelationId,
  LogLevel,
  type LogContext,
  type LogEntry,
  type LogOutput
} from './logger';

// Export debug utilities
export {
  APObjectInspector,
  APSchemaValidator,
  RequestTracer,
  FederationDiagnostics,
  debugInspector,
  debugValidator,
  debugTracer,
  debugDiagnostics,
  debug,
  DebugLevel,
  DEFAULT_DEBUG_CONFIG,
  type DebugConfig,
  type RequestTrace,
  type ValidationResult,
  type DebugValidationError,
  type DebugValidationWarning,
  type DiagnosticResult,
  type DiagnosticCheck,
  type GlobalHealth
} from './debug';

// Export metrics utilities
export {
  MetricsCollector,
  HealthChecker,
  PerformanceMonitor,
  globalMetrics,
  globalHealthChecker,
  globalPerformanceMonitor,
  ActivityPubMetrics,
  registerDefaultHealthChecks,
  createMetricsMiddleware,
  initializeMetrics,
  getMetricsSummary,
  MetricType,
  HealthStatus,
  type MetricPoint,
  type HistogramBucket,
  type HistogramData,
  type TimerData,
  type HealthCheck
} from './metrics';

// Export cache utilities
export {
  MemoryCache,
  ActivityPubCache,
  globalAPCache,
  EvictionStrategy,
  type CacheEntry,
  type CacheStats,
  type CacheConfig
} from './cache';

// Export normalization utilities
export {
  normalizeLanguageMap,
  normalizeOrArray,
  normalizeOrArrayPreserveSingle,
  normalizeEntityReference,
  normalizeDates,
  normalizeAPObject,
  normalizeAPObjects,
  createNormalizedCopy,
  DEFAULT_LANGUAGE_MAP_OPTIONS,
  type LanguageMapOptions,
  type NormalizationResult
} from './normalization';

// Export content processing utilities
export {
  extractMentions,
  extractHashtags,
  extractUrls,
  mentionsToLinks,
  hashtagsToTags,
  markdownToHtml,
  sanitizeHtml,
  linkifyContent,
  getMediaType,
  validateMediaFile,
  createMediaAttachment,
  processMediaFiles,
  processContent,
  normalizeTextContent,
  truncateContent,
  getContentStats,
  needsContentWarning,
  DEFAULT_CONTENT_OPTIONS,
  type MentionMatch,
  type HashtagMatch,
  type MediaInfo,
  type ContentProcessingOptions,
  type ProcessedContent
} from './content';

// Export link utilities
export {
  getUrlFromAPObjectSafe,
  getUrlFromAPObjectRequired,
  getUrlsFromAPObjects,
  validateUrl,
  normalizeUrl,
  isSameDomain,
  isExternalUrl,
  UrlValidationPresets,
  type UrlValidationOptions
} from './links';

// Export comprehensive validation system
export {
  // Types
  type ValidationError as ValidationErrorType,
  type ValidationResult as ValidationResultType,
  type FieldSchema,
  type ObjectSchema,
  type ValidationContext,
  type ValidationOptions,

  // Constants
  DEFAULT_VALIDATION_OPTIONS,
  CORE_SCHEMAS,
  EXTENDED_SCHEMAS,
  ACTOR_SCHEMAS,
  ACTIVITY_SCHEMAS,
  COLLECTION_SCHEMAS,
  ALL_SCHEMAS,

  // Core validation functions
  validateObject,
  validateActivityPubObject,
  validateActivity as validateActivitySchema,
  validateActor,
  validateCollection,

  // Type guards
  isValidActivityPubObject,
  isValidActivity as isValidActivitySchema,
  isValidActor,
  isValidCollection,

  // Batch operations
  validateObjects,
  validateSafely,
  validateAndAssert,

  // Main validation utility
  validation
} from './validation';

// Export HTTP signature utilities
export {
  generateDigest,
  buildSignatureString,
  parseSignatureHeader,
  extractSignature,
  signRequest,
  verifySignature,
  validateIncomingSignature,
  createSignatureValidationMiddleware
} from './http-signature';

// Export HTTP client utilities
export {
  signedFetch,
  signedPost,
  signedGet,
  type SignedRequestOptions
} from './http-client';

// Export inbox validation utilities
export {
  validateInboxRequest,
  createInboxProcessingContext,
  createInboxValidationMiddleware
} from './inbox-validation';
