/**
 * HTTP client with signature support for ActivityPub federation
 */

import { signRequest } from './http-signature';
import { rateLimitedFetch } from '$lib/activitypub/rateLimiter';
import { ACTIVITYPUB_HEADERS } from './constants';
import { ActivityPubLogs } from './logger';
import { NetworkError, FetchError } from './errors';

export interface SignedRequestOptions extends RequestInit {
  privateKey: string;
  keyId: string;
  skipSigning?: boolean;
}

/**
 * Make a signed HTTP request for ActivityPub federation
 */
export async function signedFetch(
  url: string,
  options: SignedRequestOptions
): Promise<Response> {
  const {
    privateKey,
    keyId,
    skipSigning = false,
    method = 'GET',
    body,
    headers = {},
    ...fetchOptions
  } = options;

  try {
    let requestHeaders = {
      ...ACTIVITYPUB_HEADERS,
      ...headers
    };

    // Sign the request if not skipped
    if (!skipSigning) {
      const bodyString = body ? (typeof body === 'string' ? body : JSON.stringify(body)) : null;
      
      requestHeaders = await signRequest(
        method,
        url,
        requestHeaders,
        bodyString,
        privateKey,
        keyId
      );
    }

    ActivityPubLogs.federation.outgoingRequest(method, url);

    const response = await rateLimitedFetch(url, {
      ...fetchOptions,
      method,
      headers: requestHeaders,
      body: body ? (typeof body === 'string' ? body : JSON.stringify(body)) : undefined
    });

    ActivityPubLogs.federation.outgoingResponse(method, url, response.status);

    return response;

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    ActivityPubLogs.federation.outgoingError(method, url, errorMessage);

    if (error instanceof TypeError && error.message.includes('fetch')) {
      throw new NetworkError(`Network error: ${errorMessage}`, { url, method });
    }

    throw new FetchError(`Request failed: ${errorMessage}`, 'SIGNED_FETCH_ERROR', {
      url,
      method,
      error: errorMessage
    });
  }
}

/**
 * POST with signature for activity delivery
 */
export async function signedPost(
  url: string,
  activity: any,
  privateKey: string,
  keyId: string
): Promise<Response> {
  return signedFetch(url, {
    method: 'POST',
    body: JSON.stringify(activity),
    privateKey,
    keyId,
    headers: {
      'Content-Type': 'application/activity+json'
    }
  });
}

/**
 * GET with signature for fetching protected resources
 */
export async function signedGet(
  url: string,
  privateKey: string,
  keyId: string
): Promise<Response> {
  return signedFetch(url, {
    method: 'GET',
    privateKey,
    keyId
  });
}