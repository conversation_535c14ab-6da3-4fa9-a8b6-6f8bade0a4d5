/**
 * Structured logging system for ActivityPub operations
 */

/**
 * Log levels
 */
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  CRITICAL = 4
}

/**
 * Log context for ActivityPub operations
 */
export interface LogContext {
  operation: string;
  actorId?: string;
  objectId?: string;
  url?: string;
  hostname?: string;
  userId?: string;
  requestId?: string;
  duration?: number;
  status?: number;
  error?: string;
  metadata?: Record<string, any>;
}

/**
 * Structured log entry
 */
export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  context: LogContext;
  correlationId?: string;
}

/**
 * Log output interface
 */
export interface LogOutput {
  write(entry: LogEntry): Promise<void>;
}

/**
 * Console log output
 */
export class ConsoleLogOutput implements LogOutput {
  async write(entry: LogEntry): Promise<void> {
    const levelName = LogLevel[entry.level];
    const timestamp = entry.timestamp;
    const context = JSON.stringify(entry.context, null, 2);

    const logMessage = `[${timestamp}] ${levelName}: ${entry.message}\nContext: ${context}`;

    switch (entry.level) {
      case LogLevel.DEBUG:
        console.debug(logMessage);
        break;
      case LogLevel.INFO:
        console.info(logMessage);
        break;
      case LogLevel.WARN:
        console.warn(logMessage);
        break;
      case LogLevel.ERROR:
      case LogLevel.CRITICAL:
        console.error(logMessage);
        break;
    }
  }
}

/**
 * ActivityPub Logger
 */
export class ActivityPubLogger {
  private readonly outputs: LogOutput[] = [];
  private minLevel: LogLevel = LogLevel.INFO;
  private correlationId?: string;

  constructor(outputs: LogOutput[] = [new ConsoleLogOutput()]) {
    this.outputs = outputs;
  }

  setMinLevel(level: LogLevel): void {
    this.minLevel = level;
  }

  setCorrelationId(id: string): void {
    this.correlationId = id;
  }

  addOutput(output: LogOutput): void {
    this.outputs.push(output);
  }

  private async log(level: LogLevel, message: string, context: LogContext): Promise<void> {
    if (level < this.minLevel) {
      return;
    }

    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      context,
      correlationId: this.correlationId
    };

    // Write to all outputs
    await Promise.all(
      this.outputs.map(output =>
        output.write(entry).catch(error =>
          console.error('Failed to write log entry:', error)
        )
      )
    );
  }

  debug(message: string, context: LogContext): Promise<void> {
    return this.log(LogLevel.DEBUG, message, context);
  }

  info(message: string, context: LogContext): Promise<void> {
    return this.log(LogLevel.INFO, message, context);
  }

  warn(message: string, context: LogContext): Promise<void> {
    return this.log(LogLevel.WARN, message, context);
  }

  error(message: string, context: LogContext): Promise<void> {
    return this.log(LogLevel.ERROR, message, context);
  }

  critical(message: string, context: LogContext): Promise<void> {
    return this.log(LogLevel.CRITICAL, message, context);
  }

  /**
   * Create a child logger with additional context
   */
  child(additionalContext: Partial<LogContext>): ActivityPubLogger {
    const childLogger = new ActivityPubLogger(this.outputs);
    childLogger.setMinLevel(this.minLevel);
    childLogger.setCorrelationId(this.correlationId || '');

    // Override log method to merge contexts
    const originalLog = childLogger.log.bind(childLogger);
    childLogger.log = async (level: LogLevel, message: string, context: LogContext) => {
      const mergedContext = { ...additionalContext, ...context };
      return originalLog(level, message, mergedContext);
    };

    return childLogger;
  }
}

/**
 * Global ActivityPub logger instance
 */
export const logger = new ActivityPubLogger();

/**
 * Logging utilities for common ActivityPub operations
 */
export const ActivityPubLogs = {
  /**
   * Log actor operations
   */
  actor: {
    fetchStarted: (url: string, requestId?: string) =>
      logger.info('Actor fetch started', {
        operation: 'actor.fetch',
        url,
        requestId
      }),

    fetchCompleted: (url: string, actorId: string, duration: number, requestId?: string) =>
      logger.info('Actor fetch completed', {
        operation: 'actor.fetch',
        url,
        actorId,
        duration,
        requestId
      }),

    fetchFailed: (url: string, error: string, duration: number, requestId?: string) =>
      logger.error('Actor fetch failed', {
        operation: 'actor.fetch',
        url,
        error,
        duration,
        requestId
      }),

    imported: (actorId: string, url: string) =>
      logger.info('Actor imported', {
        operation: 'actor.import',
        actorId,
        url
      }),

    importFailed: (url: string, error: string) =>
      logger.error('Actor import failed', {
        operation: 'actor.import',
        url,
        error
      })
  },

  /**
   * Log post operations
   */
  post: {
    fetchStarted: (url: string, requestId?: string) =>
      logger.info('Post fetch started', {
        operation: 'post.fetch',
        url,
        requestId
      }),

    fetchCompleted: (url: string, objectId: string, duration: number, requestId?: string) =>
      logger.info('Post fetch completed', {
        operation: 'post.fetch',
        url,
        objectId,
        duration,
        requestId
      }),

    fetchFailed: (url: string, error: string, duration: number, requestId?: string) =>
      logger.error('Post fetch failed', {
        operation: 'post.fetch',
        url,
        error,
        duration,
        requestId
      }),

    imported: (objectId: string, url: string, actorId?: string) =>
      logger.info('Post imported', {
        operation: 'post.import',
        objectId,
        url,
        actorId
      }),

    importFailed: (url: string, error: string, actorId?: string) =>
      logger.error('Post import failed', {
        operation: 'post.import',
        url,
        error,
        actorId
      })
  },

  /**
   * Log activity operations
   */
  activity: {
    received: (activityType: string, actorId: string, objectId?: string) =>
      logger.info('Activity received', {
        operation: 'activity.receive',
        metadata: { activityType },
        actorId,
        objectId
      }),

    processed: (activityType: string, actorId: string, objectId?: string, duration?: number) =>
      logger.info('Activity processed', {
        operation: 'activity.process',
        metadata: { activityType },
        actorId,
        objectId,
        duration
      }),

    processingFailed: (activityType: string, actorId: string, error: string, objectId?: string) =>
      logger.error('Activity processing failed', {
        operation: 'activity.process',
        metadata: { activityType },
        actorId,
        objectId,
        error
      }),

    validated: (activityType: string, actorId: string) =>
      logger.debug('Activity validated', {
        operation: 'activity.validate',
        metadata: { activityType },
        actorId
      }),

    validationFailed: (activityType: string, actorId: string, error: string) =>
      logger.warn('Activity validation failed', {
        operation: 'activity.validate',
        metadata: { activityType },
        actorId,
        error
      })
  },

  /**
   * Log collection operations
   */
  collection: {
    fetchStarted: (url: string, requestId?: string) =>
      logger.info('Collection fetch started', {
        operation: 'collection.fetch',
        url,
        requestId
      }),

    fetchCompleted: (url: string, itemCount: number, duration: number, requestId?: string) =>
      logger.info('Collection fetch completed', {
        operation: 'collection.fetch',
        url,
        duration,
        requestId,
        metadata: { itemCount }
      }),

    fetchFailed: (url: string, error: string, duration: number, requestId?: string) =>
      logger.error('Collection fetch failed', {
        operation: 'collection.fetch',
        url,
        error,
        duration,
        requestId
      }),

    processed: (url: string, processedCount: number, failedCount: number, duration: number) =>
      logger.info('Collection processed', {
        operation: 'collection.process',
        url,
        duration,
        metadata: { processedCount, failedCount }
      })
  },

  /**
   * Log rate limiting events
   */
  rateLimit: {
    hit: (hostname: string, retryAfter?: number) =>
      logger.warn('Rate limit hit', {
        operation: 'rateLimit.hit',
        hostname,
        metadata: { retryAfter }
      }),

    queuedRequest: (hostname: string, queueLength: number) =>
      logger.debug('Request queued due to rate limit', {
        operation: 'rateLimit.queue',
        hostname,
        metadata: { queueLength }
      }),

    requestCompleted: (hostname: string, waitTime: number, status: number) =>
      logger.debug('Rate limited request completed', {
        operation: 'rateLimit.complete',
        hostname,
        duration: waitTime,
        status
      })
  },

  /**
   * Log batch operations
   */
  batch: {
    started: (operation: string, itemCount: number, batchSize: number) =>
      logger.info('Batch operation started', {
        operation: `batch.${operation}`,
        metadata: { itemCount, batchSize }
      }),

    completed: (operation: string, successful: number, failed: number, duration: number) =>
      logger.info('Batch operation completed', {
        operation: `batch.${operation}`,
        duration,
        metadata: { successful, failed }
      }),

    batchProcessed: (operation: string, batchIndex: number, successful: number, failed: number) =>
      logger.debug('Batch processed', {
        operation: `batch.${operation}`,
        metadata: { batchIndex, successful, failed }
      })
  },

  /**
   * Log security events
   */
  security: {
    signatureVerified: (actorId: string, url: string) =>
      logger.info('HTTP signature verified', {
        operation: 'security.signature.verify',
        actorId,
        url
      }),

    signatureVerificationFailed: (actorId: string, url: string, error: string) =>
      logger.warn('HTTP signature verification failed', {
        operation: 'security.signature.verify',
        actorId,
        url,
        error
      }),

    suspiciousActivity: (actorId: string, reason: string, metadata?: Record<string, any>) =>
      logger.warn('Suspicious activity detected', {
        operation: 'security.suspicious',
        actorId,
        error: reason,
        metadata
      }),

    blockedRequest: (url: string, reason: string) =>
      logger.warn('Request blocked', {
        operation: 'security.block',
        url,
        error: reason
      })
  },

  /**
   * Log federation events
   */
  federation: {
    incomingRequest: (method: string, url: string) =>
      logger.info('Incoming federation request', {
        operation: 'federation.incoming.request',
        method,
        url
      }),

    incomingResponse: (method: string, url: string, status: number) =>
      logger.info('Incoming federation response', {
        operation: 'federation.incoming.response',
        method,
        url,
        status
      }),

    incomingError: (method: string, url: string, error: string) =>
      logger.error('Incoming federation error', {
        operation: 'federation.incoming.error',
        method,
        url,
        error
      }),

    outgoingRequest: (method: string, url: string) =>
      logger.info('Outgoing federation request', {
        operation: 'federation.outgoing.request',
        method,
        url
      }),

    outgoingResponse: (method: string, url: string, status: number) =>
      logger.info('Outgoing federation response', {
        operation: 'federation.outgoing.response',
        method,
        url,
        status
      }),

    outgoingError: (method: string, url: string, error: string) =>
      logger.error('Outgoing federation error', {
        operation: 'federation.outgoing.error',
        method,
        url,
        error
      }),

    signatureGenerated: (keyId: string, url: string) =>
      logger.debug('HTTP signature generated', {
        operation: 'federation.signature.generate',
        keyId,
        url
      })
  }
} as const;

/**
 * Performance timing utility
 */
export function createTimer() {
  const start = Date.now();
  return {
    end: () => Date.now() - start
  };
}

/**
 * Generate correlation ID for request tracking
 */
export function generateCorrelationId(): string {
  return `ap_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
}
