/**
 * @deprecated This file is deprecated. Use functions from '$lib/activitypub/utils' instead.
 * This file is kept for backward compatibility only.
 */

// Re-export all functions from the new utils module for backward compatibility
export {
  processAPObject,
  processUrlsInAPObject,
  processDatesInAPObject,
  getCollectionTotalItems,
  getCollectionItemsPageByPage,
  ObjectProcessingError,
  CircularReferenceError,
  type ProcessingOptions
} from '$lib/activitypub/utils';
