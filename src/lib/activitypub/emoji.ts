import type { CoreObject } from '$lib/activitypub/types';
import { db } from '$lib/server/db';
import { emoji } from '$lib/server/db/schema';
import { getUrlFromAPObject } from '$lib/activitypub/link';
import { and, eq } from 'drizzle-orm';
import { escapeHtml, isSecureUrl } from '$lib/activitypub/utils/security';

export async function importEmojis(object: CoreObject) {
  const domain = object.id?.host;

  if (!domain) {
    return;
  }

  if (object.tag) {
    if (!Array.isArray(object.tag)) {
      object.tag = [object.tag];
    }
    await Promise.all(object.tag.filter(tag => 'type' in tag)
      .map(async tag => {
        // Мощнейше тайпгардим!
        if (tag.type !== 'Emoji'
          || !('icon' in tag)
          || !('name' in tag)
          || !tag.name
          || !tag.icon
          || Array.isArray(tag.icon)
          || !('url' in tag.icon)
          || !tag.icon.url
        ) {
          return false;
        }

        const url = getUrlFromAPObject(tag.icon);

        if (!url) {
          return false;
        }

        const existingEmoji = await db.query.emoji.findFirst({
          where: and(
            eq(emoji.code, tag.name),
            eq(emoji.domain, domain)
          )
        });

        if (existingEmoji) {
          if (existingEmoji.url !== url.toString()) {
            await db.update(emoji).set({ url }).where(eq(emoji.id, existingEmoji.id));
          }
          return false;
        }

        return db.insert(emoji).values({
          code: tag.name,
          url,
          domain
        })
      })
      .filter(Boolean)
    );
  }
}

/**
 * Creates safe HTML for emoji with validation and escaping.
 *
 * @param url - The emoji image URL
 * @param code - The emoji code (e.g., "smile")
 * @returns Safe HTML string or fallback text
 */
function createSafeEmojiHtml(url: string, code: string): string {
  // Validate URL for security
  if (!isSecureUrl(url)) {
    return `:${code}:`; // Return original code if URL is unsafe
  }

  const safeUrl = escapeHtml(url);
  const safeCode = escapeHtml(code);

  return `<img src="${safeUrl}" alt="${safeCode}" class="emoji" loading="lazy" />`;
}

export async function replaceEmojis(string: string, domain: string) {
  const emojis = await db.select().from(emoji).where(eq(emoji.domain, domain));
  const emojiMap = new Map(emojis.map(emoji => [emoji.code, emoji.url]));
  return string.replace(/:([a-zA-Z0-9_\-+]+):/g, (match, code) => {
    const url = emojiMap.get(code);
    if (url) {
      return createSafeEmojiHtml(url, code);
    }
    return match;
  });
}
