// TODO: backfill feeds

import { db } from '$lib/server/db';
import { user } from '$lib/server/db/schema';
import { and, eq, isNull } from 'drizzle-orm';
import type { Person } from '$lib/activitypub/types';
import { getUrlFromAPObject } from '$lib/activitypub/link';
import type { WebFingerResponse } from '$lib/activitypub/types/webfinger';
import { importEmojis } from '$lib/activitypub/emoji';
import {
  fetchAndProcessAPObject,
  getObjectType,
  isValidActivityPubUrl
} from '$lib/activitypub/utils';
import {
  FetchError,
  InvalidTypeError,
  MissingFieldError,
  ActorImportError,
  WebFingerError,
  InvalidUrlError,
  withErrorRecovery
} from '$lib/activitypub/utils/errors';

export function discoverActor(query: string): Promise<typeof user.$inferSelect | null> {
  if (/^(https?:)?\/\//.test(query)) {
    return discoverActorByURL(query);
  }

  const queryParts = query.split('@').filter(Boolean);
  if (queryParts.length === 2) {
    return discoverActorByHandle(query);
  }

  return findActorByUsername(query);
}

export async function discoverActorByURL(url: string): Promise<typeof user.$inferSelect | null> {
  // Validate URL format
  if (!isValidActivityPubUrl(url)) {
    throw new InvalidUrlError(url, 'actor URL');
  }

  // Fetch actor with error recovery
  const fetchResult = await fetchAndProcessAPObject<Person>(url, {
    timeout: 10000
  });

  if (!fetchResult.success) {
    throw new FetchError(
      `Failed to fetch actor: ${fetchResult.error}`,
      'ACTOR_FETCH_ERROR',
      { url, status: fetchResult.statusCode, error: fetchResult.error }
    );
  }

  const actor = fetchResult.data!;

  // Validate actor type
  const actorType = getObjectType(actor);
  if (actorType !== 'Person') {
    throw new InvalidTypeError('Person', actorType);
  }

  // Validate required fields
  if (!actor.preferredUsername) {
    throw new MissingFieldError('preferredUsername', 'Person');
  }

  if (!actor.id) {
    throw new MissingFieldError('id', 'Person');
  }

  const username = actor.preferredUsername;
  const domain = actor.id.host;

  const userInDB = await findUserByUsernameAndDomain(username, domain);
  if (userInDB) {
    return userInDB;
  }

  return importActor(actor);
}

export async function discoverActorByHandle(handle: string) {
  const handleParts = handle.split('@');
  if (handleParts.length !== 2) {
    throw new WebFingerError(handle, 'Invalid handle format');
  }

  const [username, domain] = handleParts;

  if (!username || !domain) {
    throw new WebFingerError(handle, 'Missing username or domain');
  }

  // Check if user already exists
  const existingUser = await findUserByUsernameAndDomain(username, domain);
  if (existingUser) {
    return existingUser;
  }

  // Perform WebFinger lookup with error recovery
  const webfingerUrl = `https://${domain}/.well-known/webfinger?resource=acct:${username}@${domain}`;

  const webfingerResult = await withErrorRecovery(async () => {
    const response = await fetch(webfingerUrl, {
      headers: {
        'Accept': 'application/jrd+json, application/json'
      },
      signal: AbortSignal.timeout(5000)
    });

    if (!response.ok) {
      throw new FetchError(
        `WebFinger lookup failed: ${response.status} ${response.statusText}`,
        'WEBFINGER_ERROR',
        { url: webfingerUrl, status: response.status, statusText: response.statusText }
      );
    }

    return await response.json() as WebFingerResponse;
  }, { maxRetries: 2, retryDelay: 1000 });

  if (!webfingerResult.success) {
    throw new WebFingerError(handle, webfingerResult.error?.message);
  }

  const webfinger = webfingerResult.result!;
  const actorURL = webfinger.links?.find((link) => link.rel === 'self')?.href;

  if (!actorURL) {
    throw new WebFingerError(handle, 'No actor URL found in WebFinger response');
  }

  return discoverActorByURL(actorURL);
}

export async function findActorByUsername(username: string): Promise<typeof user.$inferSelect | null> {
  return await db.query.user.findFirst({
    where: and(
      eq(user.username, username),
      isNull(user.domain)
    )
  }) ?? null;
}

export async function findUserByUsernameAndDomain(username: string, domain: string) {
  return db.query.user.findFirst({
    where: and(eq(user.username, username), eq(user.domain, domain))
  });
}

export async function importActor(actor: Person): Promise<typeof user.$inferSelect> {
  // Validate required fields with specific error messages
  const requiredFields = [
    'preferredUsername',
    'id',
    'inbox',
    'outbox'
  ] as const;

  for (const field of requiredFields) {
    if (!actor[field]) {
      throw new MissingFieldError(field, 'Person');
    }
  }

  // Extract and validate URLs
  const avatar = actor.icon ? getUrlFromAPObject(actor.icon) : null;
  const banner = actor.image ? getUrlFromAPObject(actor.image) : null;
  const inbox = getUrlFromAPObject(actor.inbox);
  const outbox = getUrlFromAPObject(actor.outbox);

  if (!inbox) {
    throw new ActorImportError('Invalid inbox URL', actor.id?.toString());
  }

  if (!outbox) {
    throw new ActorImportError('Invalid outbox URL', actor.id?.toString());
  }

  // Validate actor ID URL
  let actorUri: URL;
  try {
    actorUri = new URL(actor.id!);
  } catch {
    throw new InvalidUrlError(actor.id?.toString() || 'undefined', 'id');
  }

  // Import emojis with error handling
  try {
    await importEmojis(actor);
  } catch (error) {
    console.warn('Failed to import emojis for actor:', error);
    // Continue with actor import even if emoji import fails
  }

  type NewUser = typeof user.$inferInsert;

  const newUser: NewUser = {
    username: actor.preferredUsername!,
    domain: actorUri.host,
    showName: actor.name || '',
    uri: actorUri.toString(),
    url: actor.url?.toString() || '',
    activityPubObject: actor,
    inbox: actor.inbox.toString(),
    outbox: actor.outbox.toString(),
    following: actor.following ? getUrlFromAPObject(actor.following) : null,
    followers: actor.followers ? getUrlFromAPObject(actor.followers) : null,
    featured: actor.featured ? getUrlFromAPObject(actor.featured) : null,
    featuredTags: actor.featuredTags ? getUrlFromAPObject(actor.featuredTags) : null,
    summary: actor.summary,
    manuallyApprovedFollowers: actor.manuallyApprovesFollowers ?? false,
    publicKey: actor.publicKey?.publicKeyPem,
    tags: actor.tag,
    avatar: avatar || null,
    banner: banner || null,
    customFields: actor.attachment,
    isCat: actor.isCat ?? false,
    birthday: actor['vcard:bday'],
    address: actor['vcard:Address'],
  }

  try {
    const users = await db.insert(user).values(newUser).returning();

    if (!users[0]) {
      throw new ActorImportError('Database insert returned no result', actor.id?.toString());
    }

    return users[0];
  } catch (error) {
    if (error instanceof ActorImportError) {
      throw error;
    }

    throw new ActorImportError(
      `Database error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      actor.id?.toString()
    );
  }
}
