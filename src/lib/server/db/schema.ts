import { pgTable, varchar, jsonb, text, boolean, integer, timestamp, uuid } from 'drizzle-orm/pg-core';

export const user = pgTable('user', {
  id: uuid('id').defaultRandom().primaryKey(),
  username: varchar('username').notNull(),
  domain: varchar('domain').notNull(),
  showName: varchar('show_name').notNull(),
  uri: varchar('uri').notNull(),
  url: varchar('url').notNull(),
  activityPubObject: jsonb('activity_pub_object'),
  inbox: varchar('inbox').notNull(),
  outbox: varchar('outbox').notNull(),
  following: varchar('following'),
  followers: varchar('followers'),
  featured: varchar('featured'),
  featuredTags: varchar('featured_tags'),
  summary: text('summary'),
  manuallyApprovedFollowers: boolean('manually_approved_followers').notNull().default(false),
  publicKey: varchar('public_key'),
  privateKey: varchar('private_key'),
  tags: jsonb('tags'),
  avatar: varchar('avatar'),
  banner: varchar('banner'),
  customFields: jsonb('custom_fields'),
  isCat: boolean('is_cat').notNull().default(false),
  birthday: varchar('birthday'),
  address: varchar('address'),
});

export const emoji = pgTable('emoji', {
  id: uuid('id').defaultRandom().primaryKey(),
  code: varchar('code').notNull(),
  url: varchar('url').notNull(),
  domain: varchar('domain'),
})

export const post = pgTable('post', {
  id: uuid('id').defaultRandom().primaryKey(),
  userId: uuid('user_id').notNull().references(() => user.id),
  uri: varchar('uri').notNull(),
  type: varchar('type').notNull(),
  activityPubObject: jsonb('activity_pub_object'),
  summary: text('summary'),
  content: text('content'),
  misskeyContent: text('misskey_content'),
  editorData: jsonb('editor_data'),
  published: timestamp('published', {
    withTimezone: true,
  }).notNull(),
  attachment: jsonb('attachment'),
  sensitive: boolean('sensitive').notNull().default(false),
  replyCount: integer('reply_count').notNull().default(0),
  likeCount: integer('like_count').notNull().default(0),
  shareCount: integer('share_count').notNull().default(0),
  customReactions: jsonb('custom_reactions'),
  visibility: varchar('visibility').notNull(),
  inReplyTo: varchar('in_reply_to'),
  quoteOf: varchar('quote_of'),
  questionData: jsonb('question_data'),
});

export const activity = pgTable('activity', {
  id: uuid('id').defaultRandom().primaryKey(),
  uri: varchar('uri').notNull().unique(),
  type: varchar('type').notNull(),
  activityPubObject: jsonb('activity_pub_object').notNull(),
  actorId: uuid('actor_id').references(() => user.id),
  actorUri: varchar('actor_uri').notNull(),
  objectUri: varchar('object_uri'),
  targetUri: varchar('target_uri'),
  direction: varchar('direction').notNull(),
  status: varchar('status').notNull().default('pending'),
  published: timestamp('published', { withTimezone: true }).notNull(),
  received: timestamp('received', { withTimezone: true }).defaultNow(),
  localPostId: uuid('local_post_id').references(() => post.id),
  localUserId: uuid('local_user_id').references(() => user.id),
  processedAt: timestamp('processed_at', { withTimezone: true }),
  errorMessage: text('error_message'),
  retryCount: integer('retry_count').default(0),
});

export const follow = pgTable('follow', {
  id: uuid('id').defaultRandom().primaryKey(),
  followerId: uuid('follower_id').references(() => user.id),
  followerUri: varchar('follower_uri').notNull(),
  followingId: uuid('following_id').references(() => user.id),
  followingUri: varchar('following_uri').notNull(),
  status: varchar('status').notNull(),
  followActivityUri: varchar('follow_activity_uri'),
  acceptActivityUri: varchar('accept_activity_uri'),
  requestedAt: timestamp('requested_at', { withTimezone: true }).defaultNow(),
  acceptedAt: timestamp('accepted_at', { withTimezone: true }),
  rejectedAt: timestamp('rejected_at', { withTimezone: true }),
  isLocal: boolean('is_local').notNull(),
});

export const notification = pgTable('notification', {
  id: uuid('id').defaultRandom().primaryKey(),
  userId: uuid('user_id').notNull().references(() => user.id),
  type: varchar('type').notNull(), // 'follow' | 'like' | 'announce' | 'mention' | 'reply' | 'poll_ended'
  activityId: uuid('activity_id').references(() => activity.id),
  postId: uuid('post_id').references(() => post.id),
  fromUserId: uuid('from_user_id').references(() => user.id),
  fromUserUri: varchar('from_user_uri'),
  isRead: boolean('is_read').notNull().default(false),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow(),
  readAt: timestamp('read_at', { withTimezone: true }),
  data: jsonb('data'),
});

export const activityDelivery = pgTable('activity_delivery', {
  id: uuid('id').defaultRandom().primaryKey(),
  activityId: uuid('activity_id').notNull().references(() => activity.id),
  recipientUri: varchar('recipient_uri').notNull(),
  recipientActorUri: varchar('recipient_actor_uri').notNull(),
  status: varchar('status').notNull().default('pending'), // 'pending' | 'delivered' | 'failed' | 'permanent_failure'
  attempts: integer('attempts').notNull().default(0),
  maxAttempts: integer('max_attempts').notNull().default(5),
  nextAttemptAt: timestamp('next_attempt_at', { withTimezone: true }),
  lastAttemptAt: timestamp('last_attempt_at', { withTimezone: true }),
  lastHttpStatus: integer('last_http_status'),
  lastErrorMessage: text('last_error_message'),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow(),
  deliveredAt: timestamp('delivered_at', { withTimezone: true }),
});

export const reaction = pgTable('reaction', {
  id: uuid('id').defaultRandom().primaryKey(),
  userId: uuid('user_id').references(() => user.id),
  userUri: varchar('user_uri').notNull(),
  postId: uuid('post_id').references(() => post.id),
  postUri: varchar('post_uri').notNull(),
  type: varchar('type').notNull(), // 'like' | 'announce' | 'dislike'
  emoji: uuid().references(() => emoji.id),
  activityUri: varchar('activity_uri').notNull(),
  undoActivityUri: varchar('undo_activity_uri'),
  isActive: boolean('is_active').notNull().default(true), // false если отменено
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow(),
  undoneAt: timestamp('undone_at', { withTimezone: true }),
});

export const activityProcessing = pgTable('activity_processing', {
  id: uuid('id').defaultRandom().primaryKey(),
  activityUri: varchar('activity_uri').notNull().unique(),
  activityType: varchar('activity_type').notNull(),
  status: varchar('status').notNull().default('pending'), // 'pending' | 'processing' | 'completed' | 'failed' | 'duplicate'
  idempotencyKey: varchar('idempotency_key').notNull().unique(),
  startedAt: timestamp('started_at', { withTimezone: true }),
  completedAt: timestamp('completed_at', { withTimezone: true }),
  errorMessage: text('error_message'),
  receivedAt: timestamp('received_at', { withTimezone: true }).defaultNow(),
  sourceInbox: varchar('source_inbox'), // Какой inbox получил
  httpSignatureValid: boolean('http_signature_valid'),
  resultActivityId: uuid('result_activity_id').references(() => activity.id),
  resultData: jsonb('result_data'),
});
