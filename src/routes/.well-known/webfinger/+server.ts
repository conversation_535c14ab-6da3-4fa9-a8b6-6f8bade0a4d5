import { json } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { users } from '$lib/server/db/schema';
import { eq, and, isNull } from 'drizzle-orm';
import { PUBLIC_BASE_URL, PUBLIC_DOMAIN } from '$env/static/public';

export async function GET({ url }) {
  const resource = url.searchParams.get('resource');

  if (!resource?.startsWith('acct:')) {
    return json({ error: 'Invalid resource' }, { status: 400 });
  }

  const match = resource.slice(5).match(/^@?([^@]+)@([^@]+)$/);
  if (!match) {
    throw new Error('Invalid handle format');
  }

  const [, username, domain] = match;

  if (domain !== PUBLIC_DOMAIN) {
    return json({ error: 'Why do you even ask me? Ask another server!' }, { status: 404 });
  }

  const user = await db.select().from(users)
    .where(and(
      eq(users.username, username),
      isNull(users.domain) // локальный пользователь
    ))
    .limit(1);

  if (!user.length) {
    return json({ error: 'User not found' }, { status: 404 });
  }

  return json({
    subject: resource,
    aliases: [
      `${PUBLIC_BASE_URL}/@${username}`,
      `${PUBLIC_BASE_URL}/users/${username}`
    ],
    links: [
      {
        "rel": "http://webfinger.net/rel/profile-page",
        "type": "text/html",
        "href": `${PUBLIC_BASE_URL}/@${username}`
      },
      {
        rel: 'self',
        type: 'application/activity+json',
        href: `${PUBLIC_BASE_URL}/users/${username}`
      },
      {
        rel: 'http://ostatus.org/schema/1.0/subscribe',
        template: `${PUBLIC_BASE_URL}/authorize_interaction?uri={uri}`
      }
    ]
  });
}
