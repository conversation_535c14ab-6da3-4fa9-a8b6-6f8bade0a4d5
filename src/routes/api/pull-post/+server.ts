import { importPost } from '$lib/activitypub/posts';

export async function POST({ request }) {
  const body = await request.json();
  const url = body.url;

  try {
    const post = await importPost(url);
    return new Response(JSON.stringify(post));
  } catch (error) {
    console.error('Failed to import post');
    console.error(error);
    return new Response('Failed to import post', { status: 500 });
  }
}
