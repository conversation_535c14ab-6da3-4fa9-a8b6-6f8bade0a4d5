import { validateInboxRequest, createInboxProcessingContext } from '$lib/activitypub/utils/inbox-validation';
import { processActivityObject } from '$lib/activitypub/activities';
import { ActivityPubLogs } from '$lib/activitypub/utils/logger';

export async function POST({ request, url }) {
  try {
    // Extract request details
    const method = request.method;
    const headers: Record<string, string> = {};

    request.headers.forEach((value, key) => {
      headers[key.toLowerCase()] = value;
    });

    const rawBody = await request.text();
    const sourceIp = headers['x-forwarded-for'] || headers['x-real-ip'];

    // Validate the incoming request
    const validation = await validateInboxRequest(
      method,
      url.toString(),
      headers,
      rawBody,
      sourceIp
    );

    if (!validation.valid) {
      ActivityPubLogs.federation.incomingError(
        method,
        url.toString(),
        validation.error || 'Validation failed'
      );

      return new Response(validation.error || 'Invalid request', {
        status: validation.statusCode || 400
      });
    }

    // Create processing context
    const context = createInboxProcessingContext(
      validation.activity!,
      validation.signature,
      rawBody,
      sourceIp
    );

    ActivityPubLogs.federation.incomingRequest(method, url.toString());

    // Process the activity
    const result = await processActivityObject(validation.activity!, context);

    if (result.success) {
      ActivityPubLogs.federation.incomingResponse(method, url.toString(), 200);
      return new Response('OK', { status: 200 });
    } else {
      ActivityPubLogs.federation.incomingError(
        method,
        url.toString(),
        result.error || 'Processing failed'
      );

      return new Response(result.error || 'Processing failed', {
        status: result.shouldRetry ? 500 : 400
      });
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    ActivityPubLogs.federation.incomingError(
      request.method,
      url.toString(),
      errorMessage
    );

    console.error('Inbox processing error:', error);

    return new Response('Internal server error', { status: 500 });
  }
}
